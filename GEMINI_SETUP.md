# Google Gemini Integration Setup

This document explains how to set up Google Gemini AI for accurate receipt data extraction.

## Overview

The receipt processing system now uses Google Gemini AI models for more accurate data extraction from receipts. The system automatically falls back to the original regex-based parser if <PERSON> is not configured or fails.

## Features

- **Intelligent Extraction**: Uses Google Gemini 1.5 Flash for accurate receipt parsing
- **Structured Output**: Extracts merchant name, amounts, dates, items, and more
- **Automatic Fallback**: Falls back to regex parsing if Gemini is unavailable
- **Configurable**: Customizable model parameters via environment variables

## Setup Instructions

### 1. Get Google Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 2. Configure Environment Variables

Create a `.env` file in the project root directory:

```bash
# Copy the example file
cp .env.example .env
```

Edit the `.env` file and add your API key:

```env
# Google Gemini API Configuration
GOOGLE_GEMINI_API_KEY=your_actual_api_key_here

# Optional: Model configuration (defaults shown)
GEMINI_MODEL=gemini-1.5-flash
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_TOKENS=1000
```

### 3. Install Dependencies

The required packages should already be installed if you've run:

```bash
# Activate virtual environment
venv\Scripts\activate

# Install requirements
pip install -r requirements.txt
```

### 4. Test the Integration

Run the test script to verify everything is working:

```bash
python test_gemini_integration.py
```

## Configuration Options

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `GOOGLE_GEMINI_API_KEY` | None | Your Google Gemini API key (required) |
| `GEMINI_MODEL` | `gemini-1.5-flash` | Gemini model to use |
| `GEMINI_TEMPERATURE` | `0.1` | Model temperature (0.0-1.0) |
| `GEMINI_MAX_TOKENS` | `1000` | Maximum tokens in response |

## How It Works

1. **OCR Extraction**: Text is first extracted from receipt images/PDFs using Tesseract or EasyOCR
2. **Gemini Processing**: The extracted text is sent to Google Gemini with a structured prompt
3. **Data Validation**: The AI response is validated and cleaned
4. **Fallback**: If Gemini fails, the system uses the original regex-based parser

## Extracted Data Fields

The system extracts the following information:

- `merchant_name`: Business/store name
- `total_amount`: Final total amount paid
- `tax_amount`: Tax amount
- `subtotal`: Subtotal before tax
- `purchased_at`: Purchase date
- `payment_method`: Payment method used
- `receipt_number`: Receipt/transaction number
- `store_address`: Store address
- `store_phone`: Store phone number
- `items`: List of purchased items with details
- `extraction_method`: Method used ('gemini' or 'regex_fallback')

## Troubleshooting

### Common Issues

1. **"Gemini parser not available"**
   - Check that the API key is correctly set in `.env`
   - Verify the API key is valid and has quota remaining

2. **"Gemini parsing failed, falling back to regex parser"**
   - This is normal behavior - the system will use the fallback parser
   - Check your internet connection
   - Verify API quota hasn't been exceeded

3. **Import errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Make sure you're using the virtual environment

### API Limits

- Google Gemini has rate limits and quotas
- The free tier has generous limits for testing
- For production use, consider upgrading to a paid plan

## Security Notes

- Never commit your `.env` file to version control
- The `.env` file is already in `.gitignore`
- Keep your API key secure and don't share it publicly

## Support

If you encounter issues:

1. Check the console output for error messages
2. Run the test script to diagnose problems
3. Verify your API key and internet connection
4. Check Google AI Studio for API status and quota
