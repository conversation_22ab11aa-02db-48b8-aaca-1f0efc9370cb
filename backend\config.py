"""
Configuration module for the Receipt Processing API
Handles environment variables and API settings
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for application settings"""
    
    # Google Gemini API Configuration
    GOOGLE_GEMINI_API_KEY: Optional[str] = os.getenv("GOOGLE_GEMINI_API_KEY")
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    GEMINI_TEMPERATURE: float = float(os.getenv("GEMINI_TEMPERATURE", "0.1"))
    GEMINI_MAX_TOKENS: int = int(os.getenv("GEMINI_MAX_TOKENS", "1000"))
    
    # Fallback configuration
    USE_GEMINI_FALLBACK: bool = os.getenv("USE_GEMINI_FALLBACK", "true").lower() == "true"
    
    @classmethod
    def is_gemini_configured(cls) -> bool:
        """Check if Google Gemini API is properly configured"""
        return cls.GOOGLE_GEMINI_API_KEY is not None and cls.GOOGLE_GEMINI_API_KEY.strip() != ""
    
    @classmethod
    def get_gemini_config(cls) -> dict:
        """Get Gemini configuration as dictionary"""
        return {
            "api_key": cls.GOOGLE_GEMINI_API_KEY,
            "model": cls.GEMINI_MODEL,
            "temperature": cls.GEMINI_TEMPERATURE,
            "max_tokens": cls.GEMINI_MAX_TOKENS
        }

# Global config instance
config = Config()
